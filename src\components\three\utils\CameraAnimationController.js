import * as THREE from "three";
import { gsap } from "gsap";

/**
 * 相机动画控制器
 * 提供相机拉远、拉近、环绕等动画效果
 */
class CameraAnimationController {
  constructor(camera, controls) {
    this.camera = camera;
    this.controls = controls;

    // 动画状态
    this.isAnimating = false;
    this.currentTween = null; // GSAP动画实例

    // 动画参数
    this.startPosition = new THREE.Vector3(2.747160536761087, 19.47018894271285, 12.611999148858837);
    this.startTarget = new THREE.Vector3(-1.1675308244948364, 14.80040844399222, -8.965726130467763);
    this.targetPosition = new THREE.Vector3(6.575638848000345, 23.41259420542899, 33.620734923011156);
    this.targetTarget = new THREE.Vector3(0, 0, 0);

    this.startEarthPosition = new THREE.Vector3(3.8, 0, 17.4);
    // this.targetEarthPosition = new THREE.Vector3(0, 0, 0);
    this.targetEarthPosition = new THREE.Vector3(0, -4, -7);

    this.startEarthRotation = new THREE.Euler(-0.651592653589793, 0, 0, "XYZ");
    this.targetEarthRotation = new THREE.Euler(0, 0, 0, "XYZ");

    // 动画时间控制
    this.animationDuration = 2000; // 默认2秒

    // 回调函数
    this.onAnimationStart = null;
    this.onAnimationUpdate = null;
    this.onAnimationComplete = null;
  }

  /**
   * 设置动画持续时间
   */
  setDuration(duration) {
    this.animationDuration = duration;
    return this;
  }

  /**
   * 设置回调函数
   */
  setCallbacks({ onStart, onUpdate, onComplete }) {
    this.onAnimationStart = onStart;
    this.onAnimationUpdate = onUpdate;
    this.onAnimationComplete = onComplete;
    return this;
  }

  /**
   * 相机拉远动画
   * @param {number} distance - 拉远的距离倍数
   * @param {Object} options - 动画选项
   */
  /**
   * 使用预设位置的相机拉远动画
   * @param {Object} options - 动画选项
   */
  zoomOutWithPresetPositions(options = {}) {
    if (this.isAnimating) {
      this.stopAnimation();
    }

    const { duration = this.animationDuration, ease = "power2.inOut", onStart, onUpdate, onComplete } = options;

    // 使用预设的起始和目标位置
    // startPosition 和 targetPosition 已经在构造函数中设置好了
    // 不需要重新计算，直接使用预设值

    // 开始动画
    this.startAnimation(duration, ease, { onStart, onUpdate, onComplete });
  }

  zoomOut(distance = 2, options = {}) {
    if (this.isAnimating) {
      this.stopAnimation();
    }

    const { duration = this.animationDuration, ease = "power2.inOut", onStart, onUpdate, onComplete } = options;

    // 保存当前位置
    const currentStartPosition = new THREE.Vector3();
    const currentStartTarget = new THREE.Vector3();
    currentStartPosition.copy(this.camera.position);
    currentStartTarget.copy(this.controls.target);

    // 计算目标位置（从原点向外拉远）
    const direction = this.camera.position.clone().normalize();
    const currentDistance = this.camera.position.length();
    const targetDistance = currentDistance * distance;

    const calculatedTargetPosition = direction.multiplyScalar(targetDistance);
    const calculatedTargetTarget = this.controls.target.clone();

    // 临时保存原始预设值
    const originalStartPosition = this.startPosition.clone();
    const originalStartTarget = this.startTarget.clone();
    const originalTargetPosition = this.targetPosition.clone();
    const originalTargetTarget = this.targetTarget.clone();

    // 设置计算出的位置
    this.startPosition.copy(currentStartPosition);
    this.startTarget.copy(currentStartTarget);
    this.targetPosition.copy(calculatedTargetPosition);
    this.targetTarget.copy(calculatedTargetTarget);

    // 开始动画
    this.startAnimation(duration, ease, {
      onStart,
      onUpdate,
      onComplete: () => {
        // 恢复原始预设值
        this.startPosition.copy(originalStartPosition);
        this.startTarget.copy(originalStartTarget);
        this.targetPosition.copy(originalTargetPosition);
        this.targetTarget.copy(originalTargetTarget);

        if (onComplete) onComplete();
      },
    });
  }

  /**
   * 相机拉近动画
   * @param {number} distance - 拉近的距离倍数
   * @param {Object} options - 动画选项
   */
  zoomIn(distance = 0.5, options = {}) {
    this.zoomOut(distance, options);
  }

  /**
   * 移动到指定位置
   * @param {THREE.Vector3} targetPosition - 目标位置
   * @param {THREE.Vector3} targetLookAt - 目标观察点
   * @param {Object} options - 动画选项
   */
  moveTo(targetPosition, targetLookAt = null, options = {}) {
    if (this.isAnimating) {
      this.stopAnimation();
    }

    const { duration = this.animationDuration, ease = "power2.inOut", onStart, onUpdate, onComplete } = options;

    // 保存当前位置
    this.startPosition.copy(this.camera.position);
    this.startTarget.copy(this.controls.target);

    // 设置目标位置
    this.targetPosition.copy(targetPosition);
    this.targetTarget.copy(targetLookAt || this.controls.target);

    // 开始动画
    this.startAnimation(duration, ease, { onStart, onUpdate, onComplete });
  }

  /**
   * 环绕动画
   * @param {number} radius - 环绕半径
   * @param {number} speed - 环绕速度
   * @param {Object} options - 动画选项
   */
  orbit(radius = null, speed = 1, options = {}) {
    if (this.isAnimating) {
      this.stopAnimation();
    }

    const currentRadius = radius || this.camera.position.length();
    const { duration = 5000, ease = "none", onStart, onUpdate, onComplete } = options;

    let startAngle = Math.atan2(this.camera.position.z, this.camera.position.x);
    const animationData = { progress: 0 };

    this.currentTween = gsap.to(animationData, {
      progress: 1,
      duration: duration / 1000, // 转换为秒
      ease: ease,
      onStart: () => {
        this.isAnimating = true;
        if (onStart) onStart();
      },
      onUpdate: () => {
        const progress = animationData.progress;
        const angle = startAngle + progress * Math.PI * 2 * speed;
        const x = Math.cos(angle) * currentRadius;
        const z = Math.sin(angle) * currentRadius;

        this.camera.position.set(x, this.camera.position.y, z);
        this.camera.lookAt(this.controls.target);
        this.controls.update();

        if (onUpdate) onUpdate(progress);
      },
      onComplete: () => {
        this.isAnimating = false;
        if (onComplete) onComplete();
      },
    });
  }

  /**
   * 开始动画 - 使用GSAP
   */
  startAnimation(duration, ease = "power2.inOut", callbacks = {}) {
    const { onStart, onUpdate, onComplete } = callbacks;

    // 创建动画数据对象
    const animationData = { progress: 0 };

    // 创建GSAP动画
    this.currentTween = gsap.to(animationData, {
      progress: 1,
      duration: duration / 1000, // 转换为秒
      ease: ease,
      onStart: () => {
        this.isAnimating = true;
        if (onStart) onStart();
      },
      onUpdate: () => {
        const progress = animationData.progress;

        // 插值计算当前位置
        const currentPosition = this.startPosition.clone().lerp(this.targetPosition, progress);
        const currentTarget = this.startTarget.clone().lerp(this.targetTarget, progress);

        // 更新相机位置
        this.camera.position.copy(currentPosition);
        this.controls.target.copy(currentTarget);
        this.controls.update();

        if (onUpdate) onUpdate(progress, progress);
      },
      onComplete: () => {
        this.isAnimating = false;
        if (onComplete) onComplete();
      },
    });
  }

  /**
   * 停止动画
   */
  stopAnimation() {
    this.isAnimating = false;
    if (this.currentTween) {
      this.currentTween.kill();
      this.currentTween = null;
    }
  }

  /**
   * 获取当前动画状态
   */
  getAnimationState() {
    return {
      isAnimating: this.isAnimating,
      currentTween: this.currentTween,
    };
  }

  /**
   * 地球动画 - 从起始位置和旋转动画到目标位置和旋转（使用弧形路径）
   * @param {THREE.Group} earthGroup - 地球组对象
   * @param {Object} options - 动画选项
   * @param {number} options.arcHeight - 弧形高度偏移量（默认为-5，负值表示向下弧形）
   * @param {number} options.midPointPosition - 中间点位置比例（0=起点，0.5=中间，1=终点，默认0.5）
   * @param {number} options.positionDelay - 位移动画延迟时间（毫秒，默认500ms）
   */
  animateEarth(earthGroup, options = {}) {
    if (this.isAnimating) {
      this.stopAnimation();
    }

    const {
      duration = this.animationDuration,
      ease = "power2.inOut", // GSAP缓动函数
      arcHeight = -5,
      midPointPosition = 0.5,
      positionDelay = 500,
      onStart,
      onUpdate,
      onComplete,
    } = options;

    // 保存地球组的引用
    this.earthGroup = earthGroup;

    // 设置地球的起始位置和旋转
    this.earthGroup.position.copy(this.startEarthPosition);
    this.earthGroup.rotation.copy(this.startEarthRotation);

    // 计算弧形路径的中间点
    const midPoint = new THREE.Vector3();
    midPoint.lerpVectors(this.startEarthPosition, this.targetEarthPosition, midPointPosition);
    midPoint.y += arcHeight; // 调整中间点的y值创建弧形

    // 创建动画对象来跟踪进度
    const animationData = {
      progress: 0,
      positionProgress: 0,
      rotationProgress: 0,
    };

    // 使用GSAP创建主动画时间线
    const timeline = gsap.timeline({
      onStart: () => {
        this.isAnimating = true;
        if (onStart) onStart();
      },
      onComplete: () => {
        this.isAnimating = false;
        if (onComplete) onComplete();
      },
    });

    // 旋转动画（立即开始）
    timeline.to(
      animationData,
      {
        rotationProgress: 1,
        duration: duration / 1000, // GSAP使用秒为单位
        ease: ease,
        onUpdate: () => {
          // 计算旋转
          const startQuaternion = new THREE.Quaternion().setFromEuler(this.startEarthRotation);
          const targetQuaternion = new THREE.Quaternion().setFromEuler(this.targetEarthRotation);
          const currentQuaternion = new THREE.Quaternion();
          currentQuaternion.slerpQuaternions(startQuaternion, targetQuaternion, animationData.rotationProgress);
          const currentEarthRotation = new THREE.Euler().setFromQuaternion(currentQuaternion, "XYZ");

          this.earthGroup.rotation.copy(currentEarthRotation);
        },
      },
      0
    );

    // 位移动画（延迟开始）
    timeline.to(
      animationData,
      {
        positionProgress: 1,
        duration: duration / 1000,
        ease: ease,
        onUpdate: () => {
          // 计算弧形位移
          const currentEarthPosition = this.calculateQuadraticBezier(this.startEarthPosition, midPoint, this.targetEarthPosition, animationData.positionProgress);
          this.earthGroup.position.copy(currentEarthPosition);
        },
      },
      positionDelay / 1000
    ); // 延迟时间转换为秒

    // 总进度跟踪
    timeline.to(
      animationData,
      {
        progress: 1,
        duration: duration / 1000,
        ease: ease,
        onUpdate: () => {
          if (onUpdate) {
            onUpdate(animationData.progress, animationData.progress, {
              position: this.earthGroup.position,
              rotation: this.earthGroup.rotation,
              midPoint: midPoint,
              positionProgress: animationData.positionProgress,
              rotationProgress: animationData.rotationProgress,
              isPositionDelayed: animationData.positionProgress === 0,
            });
          }
        },
      },
      0
    );

    // 保存当前动画实例
    this.currentTween = timeline;
  }

  /**
   * 计算二次贝塞尔曲线上的点
   * @param {THREE.Vector3} p0 - 起始点
   * @param {THREE.Vector3} p1 - 控制点（中间点）
   * @param {THREE.Vector3} p2 - 结束点
   * @param {number} t - 插值参数 (0-1)
   * @returns {THREE.Vector3} 曲线上的点
   */
  calculateQuadraticBezier(p0, p1, p2, t) {
    const result = new THREE.Vector3();
    const oneMinusT = 1 - t;
    const tSquared = t * t;
    const oneMinusTSquared = oneMinusT * oneMinusT;

    // 二次贝塞尔曲线公式: B(t) = (1-t)²P0 + 2(1-t)tP1 + t²P2
    result.x = oneMinusTSquared * p0.x + 2 * oneMinusT * t * p1.x + tSquared * p2.x;
    result.y = oneMinusTSquared * p0.y + 2 * oneMinusT * t * p1.y + tSquared * p2.y;
    result.z = oneMinusTSquared * p0.z + 2 * oneMinusT * t * p1.z + tSquared * p2.z;

    return result;
  }

  /**
   * 设置地球动画的起始和目标位置/旋转
   * @param {Object} config - 配置对象
   */
  setEarthAnimationConfig(config) {
    if (config.startPosition) {
      this.startEarthPosition.copy(config.startPosition);
    }
    if (config.targetPosition) {
      this.targetEarthPosition.copy(config.targetPosition);
    }
    if (config.startRotation) {
      this.startEarthRotation.copy(config.startRotation);
    }
    if (config.targetRotation) {
      this.targetEarthRotation.copy(config.targetRotation);
    }
    return this;
  }

  /**
   * 销毁控制器
   */
  destroy() {
    this.stopAnimation();
    this.camera = null;
    this.controls = null;
    this.earthGroup = null;
    this.onAnimationStart = null;
    this.onAnimationUpdate = null;
    this.onAnimationComplete = null;
  }
}

export { CameraAnimationController };
export default CameraAnimationController;
